/**
 * 卡片渲染模块
 * 负责生成和渲染工具与教程卡片
 * 版本: v2.1.0
 * 变更记录:
 * - v2.1.0 (2025-07-24): 移除调试代码，优化生产环境性能
 * - v2.0.0 (2025-07-24): 支持新的5板块分类结构：新店开发、地理位置、数据分析、点位分析、数据教程
 * - v1.1.0 (2025-07-24): 添加事件监听器清理机制，修复内存泄漏风险
 * - v1.0.0: 初始版本
 */
class CardRenderer {
    constructor() {
        // DOM元素延迟到renderAllCards方法中获取，避免在DOM加载前获取元素
        this.businessSupportGrid = null;
        this.geoLocationGrid = null;
        this.dataAnalysisGrid = null;
        this.networkPlanningGrid = null;
        this.tutorialsGrid = null;

        // 从配置文件获取移动设备断点
        this.mobileBreakpoint = window.AppConfig?.cardRenderer?.mobileBreakpoint || 767;

        // 检测是否为移动设备
        this.isMobileDevice = window.matchMedia(`(max-width: ${this.mobileBreakpoint}px)`).matches;

        // 绑定resize处理函数，以便后续清理
        this.handleResize = () => {
            this.isMobileDevice = window.matchMedia(`(max-width: ${this.mobileBreakpoint}px)`).matches;
        };

        // 监听屏幕尺寸变化
        window.addEventListener('resize', this.handleResize);

        // 是否已销毁
        this.destroyed = false;
    }

    /**
     * 初始化渲染所有卡片
     * @param {Object} contentData 包含各分类工具和教程数据的对象
     */
    renderAllCards(contentData) {
        if (!contentData) {
            return;
        }

        // 确保在此处获取DOM元素，而不是在构造函数中
        this.businessSupportGrid = document.getElementById('business-support-grid');
        this.geoLocationGrid = document.getElementById('geo-location-grid');
        this.dataAnalysisGrid = document.getElementById('data-analysis-grid');
        this.networkPlanningGrid = document.getElementById('network-planning-grid');
        this.tutorialsGrid = document.getElementById('tutorials-grid');

        // 渲染新店开发卡片
        if (contentData.businessSupport && this.businessSupportGrid) {
            // 隐藏骨架屏
            this.hideSkeleton(this.businessSupportGrid);
            const fragment = document.createDocumentFragment();
            contentData.businessSupport.forEach((item, index) => {
                const cardElement = this.createCardElement(item, 'businessSupport', index);
                fragment.appendChild(cardElement);
            });
            this.businessSupportGrid.appendChild(fragment);
        }

        // 渲染地理位置卡片
        if (contentData.geoLocation && this.geoLocationGrid) {
            // 隐藏骨架屏
            this.hideSkeleton(this.geoLocationGrid);
            const fragment = document.createDocumentFragment();
            contentData.geoLocation.forEach((item, index) => {
                const cardElement = this.createCardElement(item, 'geoLocation', index);
                fragment.appendChild(cardElement);
            });
            this.geoLocationGrid.appendChild(fragment);
        }

        // 渲染数据分析卡片
        if (contentData.dataAnalysis && this.dataAnalysisGrid) {
            // 隐藏骨架屏
            this.hideSkeleton(this.dataAnalysisGrid);
            const fragment = document.createDocumentFragment();
            contentData.dataAnalysis.forEach((item, index) => {
                const cardElement = this.createCardElement(item, 'dataAnalysis', index);
                fragment.appendChild(cardElement);
            });
            this.dataAnalysisGrid.appendChild(fragment);
        }

        // 渲染点位分析卡片
        if (contentData.networkPlanning && this.networkPlanningGrid) {
            // 隐藏骨架屏
            this.hideSkeleton(this.networkPlanningGrid);
            const fragment = document.createDocumentFragment();
            contentData.networkPlanning.forEach((item, index) => {
                const cardElement = this.createCardElement(item, 'networkPlanning', index);
                fragment.appendChild(cardElement);
            });
            this.networkPlanningGrid.appendChild(fragment);
        }

        // 渲染教程卡片
        if (contentData.tutorials && this.tutorialsGrid) {
            // 隐藏骨架屏
            this.hideSkeleton(this.tutorialsGrid);
            const fragment = document.createDocumentFragment();
            contentData.tutorials.forEach((item, index) => {
                const cardElement = this.createCardElement(item, 'tutorials', index);
                fragment.appendChild(cardElement);
            });
            this.tutorialsGrid.appendChild(fragment);
        }

        // 通知骨架屏管理器隐藏所有骨架屏
        if (window.SkeletonManager) {
            window.SkeletonManager.hideAllSkeletons();
        }

        // 添加细腻交互效果
        this.addCardInteractionEffects();
    }

    /**
     * 隐藏指定容器中的骨架屏占位符
     * @param {HTMLElement} container 容器元素
     */
    hideSkeleton(container) {
        if (!container) return;
        const skeletonElements = container.querySelectorAll('.skeleton-placeholder');
        skeletonElements.forEach(skeleton => {
            skeleton.classList.add('skeleton-hidden');
        });
    }

    /**
     * 创建单个卡片元素
     * @param {Object} item 卡片数据对象
     * @param {String} type 卡片类型 ('tools' 或 'tutorials')
     * @param {Number} index 在数组中的索引
     * @returns {HTMLElement} 卡片DOM元素
     */
    createCardElement(item, type, index) {
        // 创建卡片容器
        const card = document.createElement('div');
        card.className = 'card group relative rounded-lg p-2 sm:p-3 flex flex-col items-start text-left cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-primary/20 hover:-translate-y-1 scroll-reveal';
        card.dataset.type = type;
        card.dataset.index = index;
        // 添加数据ID以支持新的数据管理器
        card.dataset.id = item.id || `${type}-${index}`;
        
        // 添加状态标签
        const statusBadge = this.generateStatusBadge(item);
        if (statusBadge) {
            card.appendChild(statusBadge);
        }
        
        // 添加卡片光晕效果
        const cardGlow = document.createElement('div');
        cardGlow.className = 'card-glow w-full h-full absolute inset-0';
        card.appendChild(cardGlow);
        
        // 创建图标和标题的水平容器
        const headerContainer = document.createElement('div');
        headerContainer.className = 'flex items-center gap-2 mb-1 sm:mb-2 w-full';

        // 添加图标（缩小尺寸）
        const icon = document.createElement('i');
        icon.className = `${item.icon} text-primary text-sm sm:text-base md:text-lg flex-shrink-0 transition-transform duration-300 group-hover:scale-110 group-hover:text-primary-dark`;
        headerContainer.appendChild(icon);

        // 添加标题
        const title = document.createElement('h3');
        title.className = 'text-xs sm:text-sm md:text-base font-bold text-gray-800 group-hover:text-primary-dark transition-colors duration-300 line-clamp-1 flex-1 min-w-0';
        title.textContent = item.name;
        headerContainer.appendChild(title);

        card.appendChild(headerContainer);
        
        // 添加描述
        const description = document.createElement('p');
        description.className = 'text-xs sm:text-xs text-gray-600 flex-grow line-clamp-2';
        description.textContent = item.description;
        card.appendChild(description);
        
        // 添加细微的装饰元素，提升精致感
        this.addDecorativeElements(card, item);
        
        return card;
    }

    /**
     * 为卡片添加装饰元素，提升精致感
     * @param {HTMLElement} card 卡片DOM元素
     * @param {Object} item 卡片数据对象
     */
    addDecorativeElements(card, item) {
        // 移除细线装饰，不再添加动态线条
    }

    /**
     * 添加卡片的细腻交互效果
     * 为已渲染的卡片添加额外的交互效果
     */
    addCardInteractionEffects() {
        // 为所有卡片添加鼠标跟踪效果
        const cards = document.querySelectorAll('.card');
        
        cards.forEach(card => {
            // 只在非移动设备上添加鼠标跟踪效果
            if (!this.isMobileDevice) {
                // 预先缓存卡片内的元素，避免在mousemove中重复查询DOM
                const glowElement = card.querySelector('.card-glow');
                const iconElement = card.querySelector('.icon') || card.querySelector('i');
                const titleElement = card.querySelector('h3');
                const descElement = card.querySelector('p');
                
                // 使用节流函数来减少mousemove事件处理的频率
                let ticking = false;
                
                // 鼠标移动光晕跟踪效果
                card.addEventListener('mousemove', e => {
                    if (!ticking) {
                        window.requestAnimationFrame(() => {
                            const rect = card.getBoundingClientRect();
                            const x = e.clientX - rect.left; // 鼠标在卡片内的X坐标
                            const y = e.clientY - rect.top; // 鼠标在卡片内的Y坐标
                            
                            // 计算鼠标位置相对于卡片中心的偏移
                            const centerX = rect.width / 2;
                            const centerY = rect.height / 2;
                            const deltaX = (x - centerX) / centerX; // 归一化为 -1 到 1
                            const deltaY = (y - centerY) / centerY; // 归一化为 -1 到 1
                            
                            // 使用CSS变量一次性设置所有需要的值，减少重排/重绘
                            const tiltAmount = 4; // 倾斜角度
                            card.style.setProperty('--deltaX', deltaX);
                            card.style.setProperty('--deltaY', deltaY);
                            card.style.setProperty('--mouseX', `${x}px`);
                            card.style.setProperty('--mouseY', `${y}px`);
                            
                            // 只应用主要的3D变换，减少样式计算
                            card.style.transform = `perspective(1000px) rotateX(${-deltaY * tiltAmount}deg) rotateY(${deltaX * tiltAmount}deg) translateZ(20px)`;
                            
                            // 仅在鼠标移动时才更新光晕效果，减少DOM操作
                            if (glowElement) {
                                glowElement.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(79, 134, 198, 0.25), transparent 70%)`;
                            }
                            
                            // 使用更简单的阴影效果，减少浏览器的渲染负担
                            card.style.boxShadow = `0 10px 20px rgba(0, 0, 0, 0.1), ${deltaX * -10}px ${deltaY * -10}px 20px rgba(79, 134, 198, 0.15)`;
                            
                            // 简化子元素的变换效果
                            if (iconElement) {
                                iconElement.style.transform = `translateZ(10px)`;
                            }
                            
                            if (titleElement) {
                                titleElement.style.transform = `translateZ(5px)`;
                            }
                            
                            ticking = false;
                        });
                        
                        ticking = true;
                    }
                });
                
                // 鼠标离开时恢复正常状态
                card.addEventListener('mouseleave', () => {
                    // 一次性重置所有样式，减少多次样式更新
                    card.style.transform = '';
                    card.style.boxShadow = '';
                    
                    if (glowElement) {
                        glowElement.style.background = '';
                    }
                    
                    if (iconElement) {
                        iconElement.style.transform = '';
                    }
                    
                    if (titleElement) {
                        titleElement.style.transform = '';
                    }
                });
                
                // 为卡片添加鼠标进入和离开过渡动画
                card.addEventListener('mouseenter', () => {
                    // 应用平滑的进入过渡
                    card.style.transition = 'all 0.3s cubic-bezier(0.23, 1, 0.32, 1)';
                });
                
                card.addEventListener('mouseleave', () => {
                    // 应用平滑的离开过渡
                    card.style.transition = 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                });
            }
            
            // 点击/触摸涟漪效果增强，同时适用于桌面和移动设备
            const addRippleEffect = (e) => {
                const rect = card.getBoundingClientRect();
                const x = (e.clientX || e.touches[0].clientX) - rect.left;
                const y = (e.clientY || e.touches[0].clientY) - rect.top;
                
                const ripple = document.createElement('div');
                ripple.className = 'card-ripple';
                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;
                
                // 增加涟漪效果的大小和不透明度
                ripple.style.background = 'rgba(58, 110, 165, 0.4)';
                
                card.appendChild(ripple);
                
                // 动画结束后移除涟漪元素
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            };
            
            // 同时支持点击和触摸事件
            card.addEventListener('click', addRippleEffect);
            card.addEventListener('touchstart', addRippleEffect, { passive: true });
            
            // 移动设备上增强触摸反馈
            if (this.isMobileDevice) {
                card.addEventListener('touchstart', () => {
                    card.classList.add('active');
                }, { passive: true });
                
                card.addEventListener('touchend', () => {
                    card.classList.remove('active');
                });
            }
        });
    }

    /**
     * 生成卡片状态标签
     * @param {Object} item 卡片数据对象
     * @returns {HTMLElement|null} 状态标签DOM元素或null
     */
    generateStatusBadge(item) {
        // 强制检查特定项目
        const forceStatusItems = {
            '智能审店': '开发中',
            '地理编码转换': '开发中',
            '空间数据分析': '编写中'
        };

        let statusText = item.status;

        // 如果没有status字段，检查是否是需要强制添加状态的项目
        if (!statusText && forceStatusItems[item.name]) {
            statusText = forceStatusItems[item.name];
        }

        // 如果仍然没有状态，尝试从changelog解析
        if (!statusText && item.changelog) {
            const changelogText = Array.isArray(item.changelog) ? item.changelog.join(' ') : item.changelog;
            if (changelogText.includes('[开发中]')) statusText = '开发中';
            else if (changelogText.includes('[计划中]')) statusText = '计划中';
            else if (changelogText.includes('[编写中]')) statusText = '编写中';
        }

        // 如果没有状态，返回null
        if (!statusText) return null;

        const badge = document.createElement('div');
        badge.className = 'absolute top-1 right-1 text-xs rounded-full px-2 py-1 shadow-sm font-medium';

        // 直接设置内联样式，确保显示
        badge.style.backgroundColor = '#dbeafe';
        badge.style.color = '#1e40af';
        badge.style.zIndex = '10';
        badge.style.animation = 'pulse 2s infinite';
        badge.style.border = '1px solid rgba(59, 130, 246, 0.3)';
        badge.textContent = statusText;

        return badge;
    }

    /**
     * 销毁卡片渲染器，清理所有事件监听器
     */
    destroy() {
        // 标记为已销毁
        this.destroyed = true;

        // 移除resize事件监听器
        if (this.handleResize) {
            window.removeEventListener('resize', this.handleResize);
            this.handleResize = null;
        }

        // 清理DOM引用
        this.businessSupportGrid = null;
        this.geoLocationGrid = null;
        this.dataAnalysisGrid = null;
        this.networkPlanningGrid = null;
        this.tutorialsGrid = null;
    }
}

// 导出卡片渲染器实例
window.CardRenderer = new CardRenderer();