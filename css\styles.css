/* 
注意：关键渲染路径所需的样式已内联到HTML中
以下是非关键的增强样式，可以延迟加载
*/

/* 以下关键样式已内联到HTML中，这里仅作参考
body {
    font-family: 'Exo 2', 'Noto Sans SC', sans-serif;
    background-color: #E4ECF7;
}

.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-reveal.is-visible {
    opacity: 1;
    transform: translateY(0);
}

.card {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.01);
    background: rgba(228, 236, 247, 0.15);
    backdrop-filter: blur(6px);
    position: relative;
    overflow: hidden;
    padding: 1rem;
    border-radius: 0.5rem;
}

.card:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.02), 0 8px 10px -6px rgba(0, 0, 0, 0.01);
    background: rgba(228, 236, 247, 0.25);
}

.card:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
    transition: all 0.1s ease-out;
}

.card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, rgba(42, 84, 133, 0) 30%, rgba(79, 134, 198, 0.4) 50%, rgba(42, 84, 133, 0) 70%);
    opacity: 0;
    transition: opacity 0.8s ease, transform 0.8s ease;
    transform: translateX(-100%);
    pointer-events: none;
    z-index: 1;
}

.card:hover::before {
    opacity: 1;
    transform: translateX(100%);
}

.card-ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(58, 110, 165, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.modal-open {
    overflow: hidden;
}
*/

/* 非关键增强样式 - 这些样式不影响首次内容绘制，可以延迟加载 */

/* 板块标题样式 - 现代化设计，调整为更小的字体尺寸 */
.section-header {
    position: relative;
    margin-bottom: 1.25rem;
}

.section-title {
    background: linear-gradient(135deg, #2A5485 0%, #4F86C6 50%, #3A6EA5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #2A5485; /* 降级支持 */
    text-shadow: 0 2px 4px rgba(42, 84, 133, 0.1);
    letter-spacing: -0.02em;
    line-height: 1.2;
    position: relative;
    transition: all 0.3s ease;
}



/* 响应式标题优化 - 更小的字体尺寸 */
@media (max-width: 640px) {
    .section-header {
        margin-bottom: 1rem;
    }

    .section-title {
        font-size: 1.125rem !important; /* 18px */
        letter-spacing: -0.015em;
    }


}

@media (min-width: 640px) and (max-width: 1023px) {
    .section-title {
        font-size: 1.25rem !important; /* 20px */
        letter-spacing: -0.02em;
    }


}

@media (min-width: 1024px) {
    .section-title {
        font-size: 1.5rem !important; /* 24px */
        letter-spacing: -0.025em;
    }


}

/* 4列布局容器宽度限制 - 确保每行恰好显示4个卡片 */
#business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

/* 在小屏幕上调整最大宽度以适应2列布局 */
@media (max-width: 639px) {
    #business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
        max-width: 400px;
    }
}

/* 页面区域过渡增强 - 移除硬边界，创建平滑过渡 */
header {
    border-bottom: none;
    transition: background-color 0.3s ease;
}

/* 导航栏描述文字样式 */
header nav p {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.3;
    max-width: 280px;
    transition: color 0.3s ease;
}

header nav p:hover {
    color: #4f86c6;
}

/* 响应式优化 - 在较小的桌面屏幕上调整描述文字 */
@media (min-width: 768px) and (max-width: 1024px) {
    header nav p {
        font-size: 0.8rem;
        max-width: 240px;
    }
}

/* 大屏幕上的优化 */
@media (min-width: 1280px) {
    header nav p {
        font-size: 0.9rem;
        max-width: 320px;
    }
}

main section {
    position: relative;
    transition: opacity 0.4s ease;
}

main section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 10%;
    right: 10%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(226, 232, 240, 0.3), transparent);
    pointer-events: none;
}

main section:last-of-type::after {
    display: none;
}

/* 卡片样式增强 - 更细腻的阴影和过渡效果 */
.card {
    /* 升级阴影效果，使用多层阴影创造更精致的深度感 */
    box-shadow: 
        0 5px 15px rgba(0, 0, 0, 0.04),
        0 10px 30px rgba(0, 0, 0, 0.03),
        0 15px 45px rgba(79, 134, 198, 0.05);
    /* 改进过渡效果，使用更自然的缓动曲线 */
    transition: 
        transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
        box-shadow 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        background-color 0.3s ease;
    /* 移除边框效果 */
    border: none;
    /* 更精致的玻璃特效背景色 */
    background: rgba(228, 236, 247, 0.25);
    /* 增强玻璃模糊效果 */
    backdrop-filter: blur(10px);
    /* 添加微弱的内部边框光晕 */
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card:hover {
    /* 更精致的悬停效果 */
    transform: translateY(-8px) scale(1.02);
    /* 多层次阴影，创造更自然的提升感 */
    box-shadow: 
        0 10px 20px -2px rgba(0, 0, 0, 0.05),
        0 15px 30px -5px rgba(0, 0, 0, 0.04),
        0 20px 40px -8px rgba(79, 134, 198, 0.08);
    /* 悬停时更突出的玻璃效果 */
    background: rgba(228, 236, 247, 0.35);
    /* 加强内部边框光晕 */
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.card:active {
    transform: translateY(-4px) scale(0.99);
    /* 点击时使用更紧凑的阴影效果 */
    box-shadow: 
        0 5px 15px -2px rgba(0, 0, 0, 0.06),
        0 8px 20px -4px rgba(79, 134, 198, 0.1);
    transition: all 0.2s ease-out;
}

/* 桌面端卡片增强 - 增加内边距使卡片更宽，优化水平布局高度 */
@media (min-width: 640px) {
    .card {
        padding: 1rem;
        min-height: 6rem;
    }
}

/* 移动端优化样式 */
@media (max-width: 767px) {
    .card {
        /* 更合适的间距 */
        padding: 0.75rem;
        /* 更小的圆角 */
        border-radius: 0.375rem;
        /* 更轻的阴影 */
        box-shadow: 
            0 3px 8px rgba(0, 0, 0, 0.03),
            0 5px 15px rgba(0, 0, 0, 0.02);
        /* 更小的尺寸适合两列布局，优化水平布局高度 */
        min-height: 4.5rem;
        /* 移动端下的玻璃效果要更轻 */
        backdrop-filter: blur(6px);
        background: rgba(228, 236, 247, 0.2);
    }
    
    /* 移动端的激活状态 */
    .card.active {
        transform: scale(0.98);
        background: rgba(228, 236, 247, 0.25);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.15s ease-out;
    }
    
    /* 移动端下不显示过渡光效 */
    .card::before {
        display: none;
    }
    
    /* 调整移动端下的卡片标题大小 */
    .card h3 {
        font-size: 0.75rem;
        line-height: 1.2;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* 调整移动端下的卡片描述文本大小 */
    .card p {
        font-size: 0.7rem;
        line-height: 1.2;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        max-height: 2.4rem;
    }
    
    /* 调整移动端下的卡片图标大小 */
    .card .icon {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }
    
    /* 调整移动端下的状态标签 */
    .card .absolute.top-1 {
        font-size: 0.65rem;
        line-height: 1;
        padding: 0.1rem 0.25rem;
    }
}

/* 超小屏幕优化 (小于400px) */
@media (max-width: 400px) {
    .card {
        padding: 0.4rem;
        min-height: 4rem;
    }
    
    .card h3 {
        font-size: 0.7rem;
        margin-bottom: 0.125rem;
    }
    
    .card p {
        font-size: 0.65rem;
        -webkit-line-clamp: 2;
    }
    
    .card .icon {
        font-size: 1.125rem;
        margin-bottom: 0.125rem;
    }
    
    .card .absolute.top-1 {
        font-size: 0.6rem;
        padding: 0.05rem 0.2rem;
    }
}

/* 改进卡片光晕效果 */
.card::before {
    /* 更微妙的梯度光晕效果 */
    background: linear-gradient(
        120deg, 
        rgba(42, 84, 133, 0) 20%, 
        rgba(79, 134, 198, 0.35) 45%, 
        rgba(120, 162, 210, 0.3) 55%, 
        rgba(42, 84, 133, 0) 80%
    );
    /* 更平滑的过渡 */
    transition: opacity 1s ease, transform 1.2s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* 卡片内部元素增强 */
.card .icon {
    /* 增强图标过渡效果 */
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), color 0.3s ease;
    /* 添加微妙的文本阴影 */
    text-shadow: 0 2px 6px rgba(79, 134, 198, 0.1);
}

.card:hover .icon {
    transform: scale(1.15) translateY(-2px);
}

/* 卡片标题增强 */
.card h3 {
    transition: color 0.3s ease, transform 0.3s ease;
}

.card:hover h3 {
    transform: translateX(2px);
}

/* 卡片描述文本增强 */
.card p {
    transition: color 0.3s ease;
}

.card:hover p {
    color: #4a4f59;
}

/* 卡片上的自定义渐变光晕效果 */
.card-glow::before {
    content: none;
    /* 禁用原来的渐变光晕效果 */
}

.group:hover .card-glow::before {
    opacity: 0;
}

/* 引用样式装饰 */
.card::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.5) 0%,
        rgba(79, 134, 198, 0.3) 50%,
        rgba(42, 84, 133, 0.2) 100%
    );
    -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.card:hover::after {
    opacity: 1;
}

/* 添加额外的视觉效果增强 */
.card .icon {
    transition: transform 0.3s ease;
}

.card:hover .icon {
    transform: scale(1.1);
}

/* 模态框额外样式 */
#modal-panel {
    max-height: 90vh;
    overflow-y: auto;
}

/* 其他增强样式 */
::selection {
    background-color: rgba(58, 110, 165, 0.2);
    color: #2A5485;
}

/* 链接悬停效果 */
a:not(.card) {
    transition: color 0.2s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(228, 236, 247, 0.5);
}

::-webkit-scrollbar-thumb {
    background: rgba(58, 110, 165, 0.3);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(58, 110, 165, 0.5);
} 