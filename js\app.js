/**
 * 主应用模块
 * 负责协调和初始化所有其他模块
 * 版本: v1.3.0
 * 变更记录:
 * - v1.3.0 (2025-07-27): 集成不蒜子访问统计模块
 * - v1.2.0 (2025-07-24): 移除调试代码，优化生产环境性能
 * - v1.1.0 (2025-07-24): 添加全局销毁方法，修复内存泄漏风险
 * - v1.0.0: 初始版本
 */
class App {
    constructor() {
        // 初始时不检查模块存在性，延迟到DOM加载完成后再检查
        this.hasCardRenderer = false;
        this.hasScrollAnimation = false;
        this.hasModal = false;
        this.hasStatistics = false;
        this.hasContentData = false;

        // 记录初始化状态
        this.initialized = false;

        // 从配置文件获取重试参数
        this.maxRetries = window.AppConfig?.app?.maxRetries || 3;
        this.retryDelayBase = window.AppConfig?.app?.retryDelayBase || 500;
        this.retryCount = 0;

        // 是否已销毁
        this.destroyed = false;
    }

    /**
     * 初始化应用
     */
    init() {
        // 在DOM加载完成后执行初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initWithRetry();
            });
        } else {
            // 如果DOM已经加载完成，直接初始化
            this.initWithRetry();
        }
    }
    
    /**
     * 带重试的初始化
     */
    initWithRetry() {
        this.retryCount++;

        this.checkDependencies();

        if (this.hasContentData) {
            this.initModules();
            this.initialized = true;
        } else if (this.retryCount < this.maxRetries) {
            const delay = this.retryDelayBase * this.retryCount;
            // 延迟重试，每次延迟时间递增
            setTimeout(() => this.initWithRetry(), delay);
        } else {
            // 创建应急数据
            this.createEmergencyData();
            this.initModules();
        }
    }
    
    /**
     * 创建应急数据
     */
    createEmergencyData() {
        window.contentData = {
            tools: [
                {
                    name: "数据加载失败",
                    description: "请刷新页面或检查网络连接后重试",
                    icon: "fas fa-exclamation-triangle",
                    link: "#",
                    changelog: "[错误] 内容数据加载失败。\n请刷新页面重试。"
                }
            ],
            tutorials: []
        };
        this.hasContentData = true;
    }

    /**
     * 检查依赖项
     */
    checkDependencies() {
        // 检查各个模块是否存在
        this.hasCardRenderer = !!window.CardRenderer;
        this.hasScrollAnimation = !!window.ScrollAnimation;
        this.hasModal = !!window.Modal;
        this.hasStatistics = !!window.Statistics;
        this.hasContentData = !!window.contentData;
    }

    /**
     * 初始化所有模块
     */
    initModules() {
        // 检查必要的数据是否存在
        if (!this.hasContentData) {
            return;
        }

        // 初始化卡片渲染器
        if (this.hasCardRenderer) {
            try {
                window.CardRenderer.renderAllCards(window.contentData);
            } catch (error) {
                // 静默处理错误，避免影响用户体验
            }
        }

        // 初始化滚动动画
        if (this.hasScrollAnimation) {
            try {
                window.ScrollAnimation.init();
            } catch (error) {
                // 静默处理错误，避免影响用户体验
            }
        }

        // 初始化模态框
        if (this.hasModal) {
            try {
                window.Modal.init();
            } catch (error) {
                // 静默处理错误，避免影响用户体验
            }
        }

        // 初始化访问统计
        if (this.hasStatistics) {
            try {
                const statistics = new window.Statistics();
                statistics.init();
                // 保存实例引用以便销毁时清理
                this.statisticsInstance = statistics;
            } catch (error) {
                // 静默处理错误，避免影响用户体验
            }
        }
    }

    /**
     * 销毁应用，清理所有模块资源
     */
    destroy() {
        // 标记为已销毁
        this.destroyed = true;

        // 销毁各个模块
        if (this.hasScrollAnimation && window.ScrollAnimation && typeof window.ScrollAnimation.destroy === 'function') {
            window.ScrollAnimation.destroy();
        }

        if (this.hasModal && window.Modal && typeof window.Modal.destroy === 'function') {
            window.Modal.destroy();
        }

        if (this.hasCardRenderer && window.CardRenderer && typeof window.CardRenderer.destroy === 'function') {
            window.CardRenderer.destroy();
        }

        if (this.statisticsInstance && typeof this.statisticsInstance.destroy === 'function') {
            this.statisticsInstance.destroy();
            this.statisticsInstance = null;
        }

        // 重置状态
        this.hasCardRenderer = false;
        this.hasScrollAnimation = false;
        this.hasModal = false;
        this.hasStatistics = false;
        this.hasContentData = false;
        this.initialized = false;
        this.retryCount = 0;
    }
}

// 创建应用实例并初始化
const app = new App();
app.init();

// 导出应用实例，以便在需要时调用销毁方法
window.App = app;