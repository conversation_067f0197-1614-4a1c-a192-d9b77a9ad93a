/**
 * 应用配置文件
 * 统一管理所有常量和配置参数
 * 版本: v1.2.0
 * 变更记录:
 * - v1.2.0 (2025-07-27): 添加不蒜子访问统计配置
 * - v1.1.0 (2025-07-24): 移除调试配置，优化生产环境
 * - v1.0.0 (2025-07-24): 初始版本，提取所有魔法数字为配置常量
 */

// 应用配置对象
window.AppConfig = {
    // 应用初始化配置
    app: {
        // 最大重试次数
        maxRetries: 3,
        // 重试延迟基数（毫秒）
        retryDelayBase: 500
    },

    // 滚动动画配置
    scrollAnimation: {
        // 交叉观察器阈值（元素可见百分比）
        threshold: 0.1,
        // 根边距（提前触发动画）
        rootMargin: '0px 0px -10% 0px'
    },

    // 模态框配置
    modal: {
        // 涟漪动画持续时间（毫秒）
        rippleAnimationDuration: 600,
        // 模态框显示延迟（毫秒）
        showDelay: 10,
        // 模态框隐藏延迟（毫秒）
        hideDelay: 300
    },

    // 卡片渲染器配置
    cardRenderer: {
        // 移动设备断点（像素）
        mobileBreakpoint: 767
    },

    // 状态标签配置
    statusBadges: {
        // 开发状态标签配置
        development: {
            className: 'status-badge bg-blue-100 text-blue-800 animate-pulse',
            tags: ['[开发中]', '[计划中]', '[编写中]']
        },
        // 特殊标签配置
        special: {
            'AI数据分析': {
                className: 'bg-emerald-100 text-emerald-600',
                text: 'DeepSeek集成'
            }
        }
    },

    // 动画配置
    animations: {
        // 卡片悬停动画时间（毫秒）
        cardHoverDuration: 300,
        // 滚动动画时间（毫秒）
        scrollRevealDuration: 6100,
        // 过渡动画缓动函数
        easingFunctions: {
            smooth: 'cubic-bezier(0.23, 1, 0.32, 1)',
            bounce: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
            ease: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        }
    },

    // 性能配置
    performance: {
        // 节流延迟（毫秒）
        throttleDelay: 16,
        // 防抖延迟（毫秒）
        debounceDelay: 250
    },

    // 访问统计配置
    statistics: {
        // 不蒜子脚本URL
        scriptUrl: '//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js',
        // 统计项显示配置
        display: {
            // 是否显示网站总访问量
            showSitePV: true,
            // 是否显示独立访客数
            showSiteUV: true,
            // 是否显示页面访问量
            showPagePV: false
        },
        // 显示文本配置
        text: {
            sitePV: '本站总访问量',
            siteUV: '独立访客数',
            pagePV: '本页访问量',
            unit: '次',
            separator: ' | '
        },
        // 错误处理配置
        errorHandling: {
            // 脚本加载超时时间（毫秒）
            scriptTimeout: 10000,
            // 数据加载超时时间（毫秒）
            dataTimeout: 5000,
            // 最大重试次数
            maxRetries: 3,
            // 重试间隔（毫秒）
            retryInterval: 2000
        },
        // 样式配置
        styles: {
            // 容器样式类
            containerClass: 'text-sm text-gray-500 mt-2',
            // 数值样式类
            valueClass: 'font-medium text-primary',
            // 隐藏状态样式类
            hiddenClass: 'opacity-0 transition-opacity duration-300',
            // 显示状态样式类
            visibleClass: 'opacity-100 transition-opacity duration-300'
        }
    }
};

// 冻结配置对象，防止意外修改
Object.freeze(window.AppConfig);
