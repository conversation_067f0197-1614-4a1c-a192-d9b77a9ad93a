/**
 * 滚动动画模块
 * 处理页面元素在滚动时的动画效果
 * 版本: v1.2.0
 * 变更记录:
 * - v1.2.0 (2025-07-24): 移除调试代码，优化生产环境性能
 * - v1.1.0 (2025-07-24): 修复内存泄漏风险，添加资源清理机制
 * - v1.0.0: 初始版本
 */
class ScrollAnimation {
    constructor() {
        this.animatedElements = [];
        this.observer = null;
        this.initialized = false;
        // 添加缓存，记录已经处理过的元素
        this.processedElements = new Set();
        // 批量处理队列
        this.animationQueue = [];
        // 是否正在处理批量更新
        this.isBatchProcessing = false;
        // 批量处理器的ID，用于停止循环
        this.batchProcessorId = null;
        // 是否已销毁
        this.destroyed = false;
    }

    /**
     * 初始化滚动动画
     */
    init() {
        if (this.initialized) {
            return;
        }

        // 获取所有需要动画的元素
        this.animatedElements = document.querySelectorAll('.scroll-reveal');

        // 如果没有可动画元素，则返回
        if (this.animatedElements.length === 0) {
            return;
        }
        
        // 从配置文件获取观察器参数
        const config = window.AppConfig?.scrollAnimation || {};
        const threshold = config.threshold || 0.1;
        const rootMargin = config.rootMargin || '0px 0px -10% 0px';

        // 创建交叉观察器
        this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {
            threshold: threshold, // 当元素可见百分比达到阈值时触发
            rootMargin: rootMargin // 提前触发一点，使体验更流畅
        });

        // 开始观察所有元素
        this.animatedElements.forEach(element => {
            // 避免重复观察已处理的元素
            if (!this.processedElements.has(element)) {
                this.observer.observe(element);
            }
        });
        
        // 启动批量处理计时器
        this.startBatchProcessor();

        this.initialized = true;
    }

    /**
     * 启动批量处理器
     */
    startBatchProcessor() {
        // 如果已经销毁，不启动处理器
        if (this.destroyed) {
            return;
        }

        // 使用requestAnimationFrame确保在下一帧处理，减少重绘
        const processBatch = () => {
            // 检查是否已销毁
            if (this.destroyed) {
                return;
            }

            if (this.animationQueue.length > 0) {
                this.isBatchProcessing = true;

                // 获取当前帧中需要处理的所有元素
                const elementsToProcess = [...this.animationQueue];
                this.animationQueue = [];

                // 批量添加可见类
                elementsToProcess.forEach(element => {
                    element.classList.add('is-visible');
                    this.processedElements.add(element);
                });

                this.isBatchProcessing = false;
            }

            // 只有在未销毁时才继续下一帧的处理
            if (!this.destroyed) {
                this.batchProcessorId = requestAnimationFrame(processBatch);
            }
        };

        // 开始批量处理循环
        this.batchProcessorId = requestAnimationFrame(processBatch);
    }

    /**
     * 处理元素进入视口的回调函数
     * @param {IntersectionObserverEntry[]} entries 观察条目
     */
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 当元素进入视口时，将其添加到批处理队列
                if (!this.processedElements.has(entry.target)) {
                    this.animationQueue.push(entry.target);
                    // 一旦元素可见就停止观察以节省资源
                    this.observer.unobserve(entry.target);
                }
            }
        });
    }

    /**
     * 销毁观察器和事件监听器
     */
    destroy() {
        // 标记为已销毁，停止所有处理
        this.destroyed = true;

        // 停止批量处理器
        if (this.batchProcessorId) {
            cancelAnimationFrame(this.batchProcessorId);
            this.batchProcessorId = null;
        }

        // 断开观察器
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        // 清理所有数据
        this.animatedElements = [];
        this.processedElements.clear();
        this.animationQueue = [];
        this.initialized = false;
        this.isBatchProcessing = false;
    }
}

// 导出滚动动画实例
window.ScrollAnimation = new ScrollAnimation();