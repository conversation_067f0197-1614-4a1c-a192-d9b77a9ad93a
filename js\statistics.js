/**
 * 访问统计模块
 * 负责集成和管理不蒜子访问统计功能
 * 版本: v1.1.0
 * 变更记录:
 * - v1.1.0 (2025-07-27): 优化脚本检测逻辑，改进数据加载检测机制
 * - v1.0.0 (2025-07-27): 初始版本，集成不蒜子访问统计功能
 */

class Statistics {
    constructor() {
        // 从配置文件获取配置
        this.config = window.AppConfig?.statistics || {};
        
        // 初始化状态
        this.initialized = false;
        this.scriptLoaded = false;
        this.dataReady = false;
        this.retryCount = 0;
        
        // DOM元素引用
        this.container = null;
        this.elements = {};
        
        // 定时器引用
        this.scriptTimer = null;
        this.dataTimer = null;
        this.retryTimer = null;
        
        // 绑定方法上下文
        this.handleScriptLoad = this.handleScriptLoad.bind(this);
        this.handleScriptError = this.handleScriptError.bind(this);
        this.checkDataReady = this.checkDataReady.bind(this);
    }

    /**
     * 初始化统计功能
     */
    init() {
        if (this.initialized) {
            return;
        }

        try {
            // 创建显示容器
            this.createContainer();

            // 检查脚本是否已加载或数据是否已存在
            if (this.isDataAvailable()) {
                this.handleScriptLoad();
            } else {
                // 监听脚本加载
                this.waitForScript();
            }

            this.initialized = true;
        } catch (error) {
            this.handleError('初始化失败', error);
        }
    }

    /**
     * 创建统计显示容器
     */
    createContainer() {
        // 查找页脚容器
        const footer = document.querySelector('footer');
        if (!footer) {
            throw new Error('未找到页脚容器');
        }

        // 创建统计容器
        this.container = document.createElement('div');
        this.container.id = 'site-statistics';
        this.container.className = this.config.styles?.containerClass || 'text-sm text-gray-500 mt-2';
        this.container.style.opacity = '0';

        // 创建统计元素
        const statisticsHTML = this.buildStatisticsHTML();
        this.container.innerHTML = statisticsHTML;

        // 插入到页脚版权信息之前
        const copyright = footer.querySelector('p');
        if (copyright) {
            footer.insertBefore(this.container, copyright);
        } else {
            footer.appendChild(this.container);
        }

        // 缓存元素引用
        this.cacheElements();
    }

    /**
     * 构建统计HTML
     */
    buildStatisticsHTML() {
        const { display, text } = this.config;
        const valueClass = this.config.styles?.valueClass || 'font-medium text-primary';
        const separator = text?.separator || ' | ';

        const items = [];

        // 网站总访问量
        if (display?.showSitePV) {
            items.push(`
                <span id="busuanzi_container_site_pv">
                    ${text?.sitePV || '本站总访问量'}
                    <span id="busuanzi_value_site_pv" class="${valueClass}">--</span>
                    ${text?.unit || '次'}
                </span>
            `);
        }

        // 独立访客数
        if (display?.showSiteUV) {
            items.push(`
                <span id="busuanzi_container_site_uv">
                    ${text?.siteUV || '独立访客数'}
                    <span id="busuanzi_value_site_uv" class="${valueClass}">--</span>
                    ${text?.unit || '次'}
                </span>
            `);
        }

        // 页面访问量
        if (display?.showPagePV) {
            items.push(`
                <span id="busuanzi_container_page_pv">
                    ${text?.pagePV || '本页访问量'}
                    <span id="busuanzi_value_page_pv" class="${valueClass}">--</span>
                    ${text?.unit || '次'}
                </span>
            `);
        }

        return items.join(separator);
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            sitePV: document.getElementById('busuanzi_container_site_pv'),
            siteUV: document.getElementById('busuanzi_container_site_uv'),
            pagePV: document.getElementById('busuanzi_container_page_pv'),
            sitePVValue: document.getElementById('busuanzi_value_site_pv'),
            siteUVValue: document.getElementById('busuanzi_value_site_uv'),
            pagePVValue: document.getElementById('busuanzi_value_page_pv')
        };
    }

    /**
     * 检查数据是否可用
     */
    isDataAvailable() {
        // 检查不蒜子全局对象或数据元素是否存在
        if (window.busuanzi) {
            return true;
        }

        // 检查统计元素是否已有数据
        const { sitePVValue, siteUVValue, pagePVValue } = this.elements || {};
        return (
            (sitePVValue && sitePVValue.textContent !== '--' && sitePVValue.textContent !== '') ||
            (siteUVValue && siteUVValue.textContent !== '--' && siteUVValue.textContent !== '') ||
            (pagePVValue && pagePVValue.textContent !== '--' && pagePVValue.textContent !== '')
        );
    }

    /**
     * 等待脚本加载
     */
    waitForScript() {
        const timeout = this.config.errorHandling?.scriptTimeout || 10000;

        // 设置超时定时器
        this.scriptTimer = setTimeout(() => {
            // 在超时前再次检查数据是否可用
            if (this.isDataAvailable()) {
                this.handleScriptLoad();
            } else {
                this.handleScriptError(new Error('脚本加载超时'));
            }
        }, timeout);

        // 检查脚本是否加载或数据是否可用
        const checkScript = () => {
            if (window.busuanzi || this.isDataAvailable()) {
                this.handleScriptLoad();
            } else {
                setTimeout(checkScript, 100);
            }
        };

        checkScript();
    }

    /**
     * 处理脚本加载成功
     */
    handleScriptLoad() {
        if (this.scriptTimer) {
            clearTimeout(this.scriptTimer);
            this.scriptTimer = null;
        }

        this.scriptLoaded = true;
        this.waitForData();
    }

    /**
     * 处理脚本加载错误
     */
    handleScriptError(error) {
        if (this.scriptTimer) {
            clearTimeout(this.scriptTimer);
            this.scriptTimer = null;
        }

        this.handleError('脚本加载失败', error);
    }

    /**
     * 等待数据准备就绪
     */
    waitForData() {
        const timeout = this.config.errorHandling?.dataTimeout || 5000;
        
        // 设置超时定时器
        this.dataTimer = setTimeout(() => {
            this.handleDataTimeout();
        }, timeout);

        // 检查数据是否准备就绪
        this.checkDataReady();
    }

    /**
     * 检查数据是否准备就绪
     */
    checkDataReady() {
        const { sitePVValue, siteUVValue, pagePVValue } = this.elements;
        
        // 检查是否有数据更新
        const hasData = (
            (sitePVValue && sitePVValue.textContent !== '--') ||
            (siteUVValue && siteUVValue.textContent !== '--') ||
            (pagePVValue && pagePVValue.textContent !== '--')
        );

        if (hasData) {
            this.handleDataReady();
        } else {
            // 继续检查
            setTimeout(this.checkDataReady, 200);
        }
    }

    /**
     * 处理数据准备就绪
     */
    handleDataReady() {
        if (this.dataTimer) {
            clearTimeout(this.dataTimer);
            this.dataTimer = null;
        }

        this.dataReady = true;
        this.showStatistics();
    }

    /**
     * 处理数据超时
     */
    handleDataTimeout() {
        if (this.dataTimer) {
            clearTimeout(this.dataTimer);
            this.dataTimer = null;
        }

        // 尝试重试
        this.retryLoadData();
    }

    /**
     * 重试加载数据
     */
    retryLoadData() {
        const maxRetries = this.config.errorHandling?.maxRetries || 3;
        const retryInterval = this.config.errorHandling?.retryInterval || 2000;

        if (this.retryCount < maxRetries) {
            this.retryCount++;
            
            this.retryTimer = setTimeout(() => {
                this.waitForData();
            }, retryInterval);
        } else {
            this.handleError('数据加载失败', new Error('超过最大重试次数'));
        }
    }

    /**
     * 显示统计信息
     */
    showStatistics() {
        if (this.container) {
            // 使用配置的可见样式类，如果没有配置则直接设置透明度
            this.container.className = this.container.className.replace(/opacity-0/, 'opacity-100');
            this.container.style.opacity = '1';
        }
    }

    /**
     * 隐藏统计信息
     */
    hideStatistics() {
        if (this.container) {
            // 使用配置的隐藏样式类，如果没有配置则直接设置透明度
            this.container.className = this.container.className.replace(/opacity-100/, 'opacity-0');
            this.container.style.opacity = '0';
        }
    }

    /**
     * 处理错误
     */
    handleError(message, error) {
        // 静默处理错误，不影响用户体验
        // 可以在开发环境中输出错误信息
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.warn(`[Statistics] ${message}:`, error);
        }
        
        // 隐藏统计容器
        this.hideStatistics();
    }

    /**
     * 销毁统计功能
     */
    destroy() {
        // 清理定时器
        if (this.scriptTimer) {
            clearTimeout(this.scriptTimer);
            this.scriptTimer = null;
        }
        
        if (this.dataTimer) {
            clearTimeout(this.dataTimer);
            this.dataTimer = null;
        }
        
        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
            this.retryTimer = null;
        }

        // 移除DOM元素
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        // 重置状态
        this.initialized = false;
        this.scriptLoaded = false;
        this.dataReady = false;
        this.retryCount = 0;
        this.container = null;
        this.elements = {};
    }
}

// 导出统计模块
window.Statistics = Statistics;
