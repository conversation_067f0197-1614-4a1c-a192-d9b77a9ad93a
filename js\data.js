/**
 * 内容数据源
 * 版本: v3.1.0
 * 更新内容: 代码清理 - 移除冗余代码，优化结构
 * 变更记录:
 * - v3.1.0 (2025-07-24): 代码清理 - 移除冗余代码，简化逻辑结构
 * - v3.0.0 (2025-07-24): 性能优化 - 分离changelog，添加索引，实现懒加载和缓存机制
 */

/**
 * 数据管理器类 - 提供高效的数据访问和缓存机制
 */
class ContentDataManager {
  constructor() {
    this.cache = new Map();
    this.changelogCache = new Map();
    this.indexMap = new Map();
    this.stats = null;
    this.initialized = false;
  }

  /**
   * 初始化数据管理器
   */
  init(rawData) {
    this.data = rawData;
    this.buildIndexes();
    this.calculateStats();
    this.initialized = true;
  }

  /**
   * 构建索引以提高查找效率
   */
  buildIndexes() {
    Object.keys(this.data).forEach(category => {
      this.data[category].forEach((item, index) => {
        const key = `${category}-${index}`;
        this.indexMap.set(key, {
          category,
          index,
          item: {
            id: key,
            name: item.name,
            description: item.description,
            link: item.link,
            icon: item.icon,
            status: item.status // 添加status字段
          }
        });
      });
    });
  }

  /**
   * 计算并缓存统计信息
   */
  calculateStats() {
    const stats = {
      total: 0,
      categories: {}
    };

    Object.keys(this.data).forEach(category => {
      const count = this.data[category].length;
      stats.categories[category] = count;
      stats.total += count;
    });

    this.stats = Object.freeze(stats);
  }

  getCategory(category) {
    if (!this.data[category]) return [];

    if (this.cache.has(category)) {
      return this.cache.get(category);
    }

    const lightData = this.data[category].map((item, index) => ({
      id: `${category}-${index}`,
      name: item.name,
      description: item.description,
      link: item.link,
      icon: item.icon,
      status: item.status, // 添加status字段
      category,
      index
    }));

    this.cache.set(category, lightData);
    return lightData;
  }

  getChangelog(category, index) {
    const key = `${category}-${index}`;

    if (this.changelogCache.has(key)) {
      return this.changelogCache.get(key);
    }

    const item = this.data[category]?.[index];
    const changelog = item?.changelog || [];
    this.changelogCache.set(key, changelog);

    this.scheduleTask(() => this.preloadAdjacent(category, index));
    return changelog;
  }

  scheduleTask(task, timeout = 1000) {
    if (window.requestIdleCallback) {
      window.requestIdleCallback(task, { timeout });
    } else {
      setTimeout(task, 100);
    }
  }

  preloadAdjacent(category, index) {
    [index - 1, index + 1].forEach(idx => {
      if (idx >= 0 && idx < this.data[category]?.length) {
        const key = `${category}-${idx}`;
        if (!this.changelogCache.has(key)) {
          const item = this.data[category][idx];
          if (item?.changelog) {
            this.changelogCache.set(key, item.changelog);
          }
        }
      }
    });
  }

  getItemById(id) {
    const indexData = this.indexMap.get(id);
    if (!indexData) return null;

    return {
      ...indexData.item,
      changelog: this.getChangelog(indexData.category, indexData.index)
    };
  }

  getStats() {
    return this.stats;
  }

  clearCache() {
    this.cache.clear();
    this.changelogCache.clear();
  }

  /**
   * 批量预加载changelog
   */
  preloadChangelogs(categories = Object.keys(this.data)) {
    const batchSize = 3;

    categories.forEach(category => {
      if (!this.data[category]) return;

      let currentIndex = 0;
      const items = this.data[category];

      const loadBatch = () => {
        const endIndex = Math.min(currentIndex + batchSize, items.length);

        for (let i = currentIndex; i < endIndex; i++) {
          const key = `${category}-${i}`;
          if (!this.changelogCache.has(key) && items[i]?.changelog) {
            this.changelogCache.set(key, items[i].changelog);
          }
        }

        currentIndex = endIndex;
        if (currentIndex < items.length) {
          this.scheduleTask(loadBatch, 2000);
        }
      };

      this.scheduleTask(loadBatch);
    });
  }
}

// 创建全局数据管理器实例
window.ContentDataManager = new ContentDataManager();

// 原始数据定义（包含完整的changelog）
const rawContentData = {
  // 新店开发工具
  "businessSupport": [
    {
      "name": "新店测算",
      "description": "快速评估新店选址的各项利润指标。",
      "link": "https://fex.tstwg.cn/",
      "icon": "fas fa-store",
      "changelog": [
        "[2025-07-01] v2.3: 显示建店成本，优化UI加载。",
        "[2025-06-27] v2.2: 新增回本预估营业额。",
        "[2025-06-25] v2.0: 新增高级财务指标。",
        "[2025-06-25] v1.9: 更新城市梯队。",
        "[2025-06-24] v1.8: 更换新的UI样式。",
        "[2025-06-11] v1.5: 调整UI界面，提升空间利用率。",
        "[2025-06-16] v1.0: 初始版本发布。"
      ]
    },
    {
      "name": "智能审店",
      "description": "智能化点位评估工具，快速分析店铺位置的商业价值。",
      "link": "https://sd.tstwg.cn",
      "icon": "fas fa-search-location",
      "status": "开发中",
      "changelog": [
        "[开发中] v0.1: 初始版本发布。"
      ]
    }
  ],

  // 地理位置工具
  "geoLocation": [
    {
      "name": "高德POI获取",
      "description": "轻松、高效的获取全国范围内任意品牌的POI点位数据。",
      "link": "https://poi.tstwg.cn/",
      "icon": "fas fa-map-marker-alt",
      "changelog": [
        "[2025-06-27] v0.8: 调整UI，优化性能。",
        "[2025-06-15] v0.7: 优化DOM操作，使用DocumentFragment提升页面加载速度。",
        "[2025-06-11] v0.6: 修复无法导出数据的bug。",
        "[2025-01-20] v0.4: 增加了多关品牌批量查询功能。",
        "[2024-12-10] v0.1: 更新内置城市列表，提升加载速度。",
        "[2024-11-01] v0.0: 初始版本发布。"
      ]
    },
    {
      "name": "经纬度距离计算",
      "description": "计算多个点位之间的经纬度距离，支持批量处理。",
      "link": "https://lat.tstwg.cn/",
      "icon": "fas fa-ruler-combined",
      "changelog": [
        "[2025-07-14] v5.2.1: 增加点位密度分析功能，正式发布。",
        "[2025-07-13] v5.1.9: 增加距离矩阵生成功能。",
        "[2025-07-12] v5.1.3: 增加中间经纬度计算功能",
        "[2025-07-01] v5.0.0: 由线下版本转为在线版，支持多点位距离计算。"
      ]
    },
    {
      "name": "经纬度逆向工具",
      "description": "自动根据经纬度逆向输出点位信息。",
      "link": "http://lat2.tstwg.cn",
      "icon": "fas fa-globe-asia",
      "changelog": [
        "[2025-06-29] v0.5: 调整布局更加紧凑，提高空间利用率。",
        "[2025-06-27] v0.4: 新增更多导出字段。",
        "[2025-06-11] v0.3: 优化导出Excel功能。",
        "[2025-06-05] v0.2: 优化API调用速率。",
        "[2025-06-04] v0.1: 初始版本发布。"
      ]
    },

    {
      "name": "地理编码转换",
      "description": "多坐标系转换工具，敬请期待。",
      "link": "#",
      "icon": "fas fa-map",
      "status": "开发中",
      "changelog": [
        "[开发中] 计划支持WGS84、GCJ02、BD09等多种坐标系互转。",
      ]
    }


  ],

  // 数据分析工具
  "dataAnalysis": [
    {
      "name": "AI数据分析",
      "description": "通过AI快速处理与可视化数据，支持任意数据表格。",
      "link": "http://data.tstwg.cn",
      "icon": "fas fa-brain",
      "changelog": [
        "[2025-03-28] v2.5: 集成DeepSeek大语言模型。",
        "[2024-11-15] v2.0: 新增智能数据清洗功能。",
        "[2024-10-05] v1.5: 增加多种图表类型支持。",
        "[2024-08-01] v1.0: 初始版本发布。"
      ]
    },
    {
      "name": "回归分析工具",
      "description": "在线回归分析工具，支持多种回归模型和数据可视化。",
      "link": "https://re.tstwg.cn",
      "icon": "fas fa-chart-line",
      "changelog": [
        "[2025-07-23] v1.0: 初始版本发布。"
      ]
    }
  ],

  // 网规专用工具
  "networkPlanning": [
    {
      "name": "城市聚客点",
      "description": "基于密度的聚类（DBSCAN）算法工具，识别有效聚客点。",
      "link": "https://dbscan.tstwg.cn",
      "icon": "fas fa-users",
      "changelog": [
        "[2025-06-30] v1.0.6: 性能优化，添加加载动画。",
        "[2025-05-15] v1.0.5: 添加用户操作手册。",
        "[2025-05-08] v1.0.4: 新增空间算法的密度参数自动优化功能。",
        "[2025-04-12] v1.0.3: 增加自定义项筛选功能。",
        "[2025-04-01] v1.0.0: 初始版本发布。"
      ]
    },
    {
      "name": "竞品可视化",
      "description": "竞品数据可视化分析工具，用于高价值商圈点位分析。",
      "link": "https://vs.tstwg.cn/",
      "icon": "fas fa-chart-pie",
      "changelog": [
        "[2025-05-30] v3.0: 新增品牌对比可视化图表。",
        "[2025-05-25] v2.1: 优化地图加载API。",
        "[2025-03-15] v2.0: 替换开源地图为高德地图。",
        "[2025-02-20] v1.1: 调整页面布局。",
        "[2025-01-10] v1.0: 初始版本发布。"
      ]
    }
  ],

  // 数据教程
  "tutorials": [
    {
      "name": "多元线性回归",
      "description": "通过可视化图表，讲解多元线性回归的原理与应用。",
      "link": "https://mlr.tstwg.cn/",
      "icon": "fas fa-project-diagram",
      "changelog": [
        "[2024-10-10] 修正了自变量共线性教程的诊断方法。",
        "[2024-09-22] 增加了案例数据集供下载。",
        "[2024-09-15] 初始版本发布。"
      ]
    },
    {
      "name": "空间数据分析",
      "description": "空间数据分析基础教程，敬请期待。",
      "link": "#",
      "icon": "fas fa-globe-americas",
      "status": "编写中",
      "changelog": [
        "[编写中] 计划涵盖空间自相关、空间插值、空间回归等主题。",
      ]
    }
  ]
};

// 初始化数据管理器
window.ContentDataManager.init(rawContentData);

// 清除可能存在的旧缓存
window.ContentDataManager.clearCache();

// 创建兼容的contentData对象，使用数据管理器提供数据
window.contentData = {
  get businessSupport() {
    return window.ContentDataManager.getCategory('businessSupport');
  },
  get geoLocation() {
    return window.ContentDataManager.getCategory('geoLocation');
  },
  get dataAnalysis() {
    return window.ContentDataManager.getCategory('dataAnalysis');
  },
  get networkPlanning() {
    return window.ContentDataManager.getCategory('networkPlanning');
  },
  get tutorials() {
    return window.ContentDataManager.getCategory('tutorials');
  },

  // 提供获取完整数据的方法
  getItemById(id) {
    return window.ContentDataManager.getItemById(id);
  },

  // 提供获取changelog的方法
  getChangelog(category, index) {
    return window.ContentDataManager.getChangelog(category, index);
  },

  // 提供统计信息
  getStats() {
    return window.ContentDataManager.getStats();
  }
};

// 预加载changelog数据
window.ContentDataManager.scheduleTask(() => {
  window.ContentDataManager.preloadChangelogs();
}, 3000);

// 兼容性访问
var contentData = window.contentData;