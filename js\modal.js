/**
 * 模态框模块
 * 负责模态框的显示、隐藏和内容填充
 * 版本: v1.2.0
 * 变更记录:
 * - v1.2.0 (2025-07-24): 移除调试代码，优化生产环境性能
 * - v1.1.0 (2025-07-24): 添加事件监听器清理机制，修复内存泄漏风险
 * - v1.0.0: 初始版本
 */
class Modal {
    constructor() {
        // 模态框元素 - 延迟到init方法中获取
        this.modal = null;
        this.overlay = null;
        this.panel = null;
        this.closeBtn = null;

        // 模态框内容元素
        this.nameEl = null;
        this.iconEl = null;
        this.descriptionEl = null;
        this.changelogEl = null;
        this.linkEl = null;

        // 卡片元素
        this.cards = null;

        // 初始化状态
        this.initialized = false;

        // 事件处理函数引用，用于清理
        this.boundHandlers = {
            keydown: null,
            cardClick: null,
            closeClick: null,
            overlayClick: null
        };

        // 是否已销毁
        this.destroyed = false;
    }

    /**
     * 初始化模态框
     */
    init() {
        if (this.initialized) {
            return;
        }
        
        // 获取模态框元素
        this.modal = document.getElementById('modal');
        if (!this.modal) {
            console.error('未找到模态框元素 #modal');
            return;
        }
        
        this.overlay = document.getElementById('modal-overlay');
        this.panel = document.getElementById('modal-panel');
        this.closeBtn = document.getElementById('modal-close');
        
        // 获取模态框内容元素
        this.nameEl = document.getElementById('modal-name');
        this.iconEl = document.getElementById('modal-icon');
        this.descriptionEl = document.getElementById('modal-description');
        this.changelogEl = document.getElementById('modal-changelog');
        this.linkEl = document.getElementById('modal-link');
        
        // 获取所有卡片 - 这一步需要等待卡片渲染完成
        this.cards = document.querySelectorAll('.card');

        // 绑定事件
        this.bindEvents();

        this.initialized = true;
    }

    /**
     * 绑定相关事件
     */
    bindEvents() {
        // 绑定事件处理函数
        this.boundHandlers.cardClick = this.handleCardClick.bind(this);
        this.boundHandlers.closeClick = this.close.bind(this);
        this.boundHandlers.overlayClick = this.close.bind(this);
        this.boundHandlers.keydown = (e) => {
            if (e.key === 'Escape' && this.modal && !this.modal.classList.contains('hidden')) {
                this.close();
            }
        };

        // 为每张卡片添加点击事件
        if (this.cards && this.cards.length > 0) {
            this.cards.forEach(card => {
                card.addEventListener('click', this.boundHandlers.cardClick);
            });
        }

        // 关闭按钮事件
        if (this.closeBtn) {
            this.closeBtn.addEventListener('click', this.boundHandlers.closeClick);
        }

        // 点击遮罩层关闭
        if (this.overlay) {
            this.overlay.addEventListener('click', this.boundHandlers.overlayClick);
        }

        // 按ESC键关闭
        document.addEventListener('keydown', this.boundHandlers.keydown);
    }

    /**
     * 处理卡片点击事件
     * @param {Event} e 点击事件对象
     */
    handleCardClick(e) {
        const card = e.currentTarget;
        const type = card.dataset.type;
        const index = parseInt(card.dataset.index);
        const id = card.dataset.id;

        // 优先使用新的数据管理器获取数据
        let data = null;
        if (id && window.contentData.getItemById) {
            data = window.contentData.getItemById(id);
        }

        // 降级到传统方式
        if (!data && window.contentData[type] && window.contentData[type][index]) {
            const item = window.contentData[type][index];
            data = {
                ...item,
                changelog: window.contentData.getChangelog ?
                    window.contentData.getChangelog(type, index) :
                    item.changelog
            };
        }

        if (!data) {
            console.error('未找到卡片数据');
            return;
        }

        data.type = type; // 添加类型信息

        // 添加涟漪效果
        this.addRippleEffect(e, card);

        // 延迟打开模态框，等待涟漪动画完成
        setTimeout(() => {
            this.open(data);
        }, 300);
    }

    /**
     * 添加卡片点击涟漪效果
     * @param {Event} e 点击事件对象
     * @param {HTMLElement} card 卡片元素
     */
    addRippleEffect(e, card) {
        // 复用现有的涟漪元素或创建新元素
        let ripple = card.querySelector('.card-ripple');
        if (!ripple) {
            ripple = document.createElement('span');
            ripple.classList.add('card-ripple');
        } else {
            // 如果存在旧的涟漪效果，重置其动画
            ripple.style.animation = 'none';
            // 触发重排以重置动画
            void ripple.offsetWidth;
        }
        
        const rect = card.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        
        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${e.clientX - rect.left - size/2}px`;
        ripple.style.top = `${e.clientY - rect.top - size/2}px`;
        ripple.style.animation = 'ripple 0.6s linear';
        
        // 仅在不存在时添加到DOM
        if (!card.contains(ripple)) {
            card.appendChild(ripple);
        }
        
        // 从配置文件获取动画持续时间
        const duration = window.AppConfig?.modal?.rippleAnimationDuration || 600;

        // 动画结束后重置涟漪元素（不移除，以便复用）
        setTimeout(() => {
            ripple.style.animation = 'none';
        }, duration);
    }

    /**
     * 打开模态框并填充数据
     * @param {Object} data 要显示的数据
     */
    open(data) {
        if (!this.modal || !data) return;
        
        // 使用DocumentFragment优化DOM操作
        const fragment = document.createDocumentFragment();
        
        // 填充数据
        this.nameEl.textContent = data.name;
        this.iconEl.className = `${data.icon} text-primary text-4xl mt-1`;
        this.descriptionEl.textContent = data.description;
        
        // 处理更新日志 - 支持数组形式
        if (data.changelog) {
            if (Array.isArray(data.changelog)) {
                // 如果是数组，使用换行符连接各项
                this.changelogEl.textContent = data.changelog.join('\n');
            } else {
                // 如果是字符串，直接使用
                this.changelogEl.textContent = data.changelog;
            }
        } else {
            this.changelogEl.textContent = '暂无更新日志';
        }
        
        // 设置链接
        this.linkEl.href = data.link || '#';
        
        // 设置链接文本
        const linkText = (data.link === "#" || !data.link) ? 
            "暂无链接" : 
            "访问" + (data.type === "tools" ? "工具" : "教程");
            
        // 使用textContent替代innerHTML以提高性能
        this.linkEl.textContent = linkText;
        
        // 单独添加图标
        const linkIcon = document.createElement('i');
        linkIcon.className = 'fas fa-external-link-alt ml-2';
        this.linkEl.innerHTML = '';
        this.linkEl.appendChild(document.createTextNode(linkText + ' '));
        this.linkEl.appendChild(linkIcon);
        
        // 设置链接状态
        if (data.link === "#" || !data.link) {
            this.linkEl.classList.add('pointer-events-none', 'bg-gray-400');
            this.linkEl.classList.remove('bg-primary', 'hover:bg-primary-dark');
        } else {
            this.linkEl.classList.remove('pointer-events-none', 'bg-gray-400');
            this.linkEl.classList.add('bg-primary', 'hover:bg-primary-dark');
        }
        
        // 显示模态框
        this.modal.classList.remove('hidden');
        document.body.classList.add('modal-open');
        
        // 从配置文件获取显示延迟时间
        const showDelay = window.AppConfig?.modal?.showDelay || 10;

        // 使用短暂延时以允许display属性在开始过渡之前应用
        setTimeout(() => {
            this.panel.classList.remove('scale-95', 'opacity-0');
        }, showDelay);
    }

    /**
     * 关闭模态框
     */
    close() {
        if (!this.modal || !this.panel) return;
        
        this.panel.classList.add('scale-95', 'opacity-0');
        document.body.classList.remove('modal-open');
        
        // 从配置文件获取隐藏延迟时间
        const hideDelay = window.AppConfig?.modal?.hideDelay || 300;

        // 等待动画完成后再隐藏
        setTimeout(() => {
            this.modal.classList.add('hidden');
        }, hideDelay);
    }

    /**
     * 销毁模态框，清理所有事件监听器
     */
    destroy() {
        // 标记为已销毁
        this.destroyed = true;

        // 移除卡片点击事件
        if (this.cards && this.boundHandlers.cardClick) {
            this.cards.forEach(card => {
                card.removeEventListener('click', this.boundHandlers.cardClick);
            });
        }

        // 移除关闭按钮事件
        if (this.closeBtn && this.boundHandlers.closeClick) {
            this.closeBtn.removeEventListener('click', this.boundHandlers.closeClick);
        }

        // 移除遮罩层事件
        if (this.overlay && this.boundHandlers.overlayClick) {
            this.overlay.removeEventListener('click', this.boundHandlers.overlayClick);
        }

        // 移除键盘事件
        if (this.boundHandlers.keydown) {
            document.removeEventListener('keydown', this.boundHandlers.keydown);
        }

        // 清理事件处理函数引用
        this.boundHandlers = {
            keydown: null,
            cardClick: null,
            closeClick: null,
            overlayClick: null
        };

        // 清理DOM引用
        this.modal = null;
        this.overlay = null;
        this.panel = null;
        this.closeBtn = null;
        this.nameEl = null;
        this.iconEl = null;
        this.descriptionEl = null;
        this.changelogEl = null;
        this.linkEl = null;
        this.cards = null;

        this.initialized = false;
    }
}

// 导出模态框实例
window.Modal = new Modal();